import { Controller } from "@hotwired/stimulus"

// Category Builder Controller - Manages bundle categories and product assignment
export default class extends Controller {
  static targets = [
    "categoriesContainer",
    "categoriesList",
    "addCategoryButton",
    "categoryTemplate",
    "fetchCategoriesButton"
  ]

  static values = {
    categories: { type: Array, default: [] }
  }

  connect() {
    console.log("📂 Category Builder Controller connected!")

    // Make controller globally accessible for modal callbacks
    window.categoryBuilderController = this

    // Initialize categories from session data
    this.initializeCategories()

    // Set up global drag & drop event listeners
    this.setupGlobalDragListeners()
  }

  disconnect() {
    // Clean up global event listeners
    this.cleanupGlobalDragListeners()

    // Clean up global reference
    if (window.categoryBuilderController === this) {
      window.categoryBuilderController = null
    }
  }

  // Initialize categories from existing data
  initializeCategories() {
    // Clear existing categories first
    this.clearAllCategories()

    if (this.categoriesValue && this.categoriesValue.length > 0) {
      this.categoriesValue.forEach(category => {
        this.renderCategory(category)
      })
    }

    // Initialize button state
    this.updateButtonState()
  }

  // Fetch categories from Fluid API
  async fetchCategories() {
    console.log("🔄 Fetching categories from Fluid API...")

    // Update button state to show loading
    const button = this.fetchCategoriesButtonTarget
    const originalText = button.textContent
    button.textContent = "🔄 Loading..."
    button.disabled = true

    try {
      const response = await fetch('/admin/categories/search', {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success && data.categories && data.categories.length > 0) {
        console.log(`✅ Successfully fetched ${data.categories.length} categories`)
        this.showCategoriesModal(data.categories)
      } else {
        console.warn("⚠️ No categories found or API returned empty result")
        alert('No categories found in your Fluid store.')
      }
    } catch (error) {
      console.error('❌ Error fetching categories:', error)
      alert('Failed to fetch categories. Please check your connection and try again.')
    } finally {
      // Restore button state
      button.textContent = originalText
      button.disabled = false
    }
  }

  // Show categories selection modal
  showCategoriesModal(categories) {
    // Create modal HTML
    const modalHtml = this.createCategoriesModalHtml(categories)

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml)

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('categoriesSelectionModal'))
    modal.show()

    // Clean up modal when hidden
    document.getElementById('categoriesSelectionModal').addEventListener('hidden.bs.modal', function() {
      this.remove()
    })
  }

  // Create categories selection modal HTML
  createCategoriesModalHtml(categories) {
    const categoriesHtml = categories.map(category => `
      <div class="col-md-6 mb-3">
        <div class="card h-100">
          <div class="card-body">
            <div class="form-check">
              <input class="form-check-input"
                     type="checkbox"
                     value="${category.id}"
                     id="category-${category.id}"
                     data-category-name="${category.name}"
                     data-category-description="${category.description || ''}">
              <label class="form-check-label" for="category-${category.id}">
                <h6 class="card-title mb-1">${category.name}</h6>
                ${category.description ? `<p class="card-text text-muted small mb-0">${category.description}</p>` : ''}
              </label>
            </div>
          </div>
        </div>
      </div>
    `).join('')

    return `
      <div class="modal fade" id="categoriesSelectionModal" tabindex="-1" aria-labelledby="categoriesSelectionModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="categoriesSelectionModalLabel">Select Categories from Fluid Store</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
              <p class="text-muted mb-3">Select categories from your Fluid store to add to your bundle:</p>

              <!-- Auto-load products option -->
              <div class="alert alert-info mb-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="autoLoadProducts" checked>
                  <label class="form-check-label" for="autoLoadProducts">
                    <strong>Auto-load products</strong> - Automatically fetch and add products from selected categories
                  </label>
                </div>
              </div>

              <div class="row">
                ${categoriesHtml}
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
              <button type="button"
                      class="btn btn-primary"
                      onclick="categoryBuilderController.addSelectedCategories()">
                Add Selected Categories
              </button>
            </div>
          </div>
        </div>
      </div>
    `
  }

  // Add selected categories from modal
  async addSelectedCategories() {
    const modal = document.getElementById('categoriesSelectionModal')
    const selectedCheckboxes = modal.querySelectorAll('input[type="checkbox"]:checked')
    const autoLoadProducts = modal.querySelector('#autoLoadProducts').checked

    // Filter out the autoLoadProducts checkbox
    const categoryCheckboxes = Array.from(selectedCheckboxes).filter(cb => cb.id !== 'autoLoadProducts')

    if (categoryCheckboxes.length === 0) {
      alert('Please select at least one category.')
      return
    }

    // Close modal first
    const bootstrapModal = bootstrap.Modal.getInstance(modal)
    if (bootstrapModal) {
      bootstrapModal.hide()
    }

    let addedCount = 0
    const categoriesToProcess = []

    // First, add all categories
    categoryCheckboxes.forEach(checkbox => {
      // Check if we've reached the maximum of 12 categories
      if (this.categoriesValue.length >= 12) {
        alert('Maximum of 12 categories allowed for bundle configuration.')
        return
      }

      const categoryId = `cat-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      const categoryName = checkbox.dataset.categoryName
      const categoryDescription = checkbox.dataset.categoryDescription
      const fluidCategoryId = checkbox.value

      const newCategory = {
        categoryId: categoryId,
        categoryName: categoryName,
        displayOrder: this.categoriesValue.length,
        selectionQuantity: 1,
        products: [],
        fluidCategoryId: fluidCategoryId, // Store original Fluid category ID
        description: categoryDescription
      }

      this.categoriesValue.push(newCategory)
      this.renderCategory(newCategory)
      addedCount++

      // Store for product loading if needed
      if (autoLoadProducts) {
        categoriesToProcess.push({ categoryId, fluidCategoryId, categoryName })
      }
    })

    // Update button state
    this.updateButtonState()
    this.updateSessionData()

    console.log(`✅ Added ${addedCount} categories from Fluid store`)

    // Auto-load products if requested
    if (autoLoadProducts && categoriesToProcess.length > 0) {
      console.log(`🔄 Auto-loading products for ${categoriesToProcess.length} categories...`)
      await this.loadProductsForCategories(categoriesToProcess)
    }
  }

  // Load products for multiple categories
  async loadProductsForCategories(categories) {
    for (const { categoryId, fluidCategoryId, categoryName } of categories) {
      try {
        console.log(`🔄 Loading products for category: ${categoryName} (${fluidCategoryId})`)

        const response = await fetch(`/admin/categories/products/${fluidCategoryId}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-CSRF-Token': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const data = await response.json()

        if (data.success && data.products && data.products.length > 0) {
          console.log(`✅ Loaded ${data.products.length} products for ${categoryName}`)

          // Add products to the category
          const category = this.findCategory(categoryId)
          if (category) {
            data.products.forEach((product, index) => {
              const newProduct = {
                productId: product.id,
                productName: product.name,
                productSku: product.sku || 'N/A',
                productPrice: product.price || 0,
                displayOrder: index,
                isDefault: index === 0 // First product is default
              }
              category.products.push(newProduct)
            })

            // Re-render the category to show new products
            this.rerenderCategory(categoryId)
          }
        } else {
          console.warn(`⚠️ No products found for category: ${categoryName}`)
        }
      } catch (error) {
        console.error(`❌ Error loading products for category ${categoryName}:`, error)
      }
    }

    // Update session data after all products are loaded
    this.updateSessionData()
    console.log(`✅ Finished auto-loading products for all categories`)
  }

  // Add new category
  addCategory() {
    // Check if we've reached the maximum of 12 categories
    if (this.categoriesValue.length >= 12) {
      alert('Maximum of 12 categories allowed for bundle configuration.')
      return
    }

    const categoryId = `cat-${Date.now()}`
    const newCategory = {
      categoryId: categoryId,
      categoryName: '',
      displayOrder: this.categoriesValue.length,
      selectionQuantity: 1,
      products: []
    }

    this.categoriesValue.push(newCategory)
    this.renderCategory(newCategory)

    // Update button state
    this.updateButtonState()

    // Focus on the new category name input
    setTimeout(() => {
      const nameInput = this.element.querySelector(`[data-category-id="${categoryId}"] input[name="categoryName"]`)
      if (nameInput) {
        nameInput.focus()
      }
    }, 100)

    this.updateSessionData()
  }

  // Add selected products from modal to category
  addSelectedProducts(event) {
    // Get selected products from the modal
    const modal = document.getElementById('productSelectionModal')
    const selectedProducts = []

    // Find all checked checkboxes in the modal
    const checkedBoxes = modal.querySelectorAll('input[type="checkbox"]:checked')

    checkedBoxes.forEach(checkbox => {
      if (checkbox.dataset.productId) {
        // This is a parent product
        selectedProducts.push({
          productId: checkbox.dataset.productId,
          productName: checkbox.closest('.product-card').querySelector('.card-title').textContent.trim(),
          productSku: checkbox.dataset.productSku || 'N/A',
          productPrice: checkbox.dataset.productPrice || 0
        })
      }
    })

    if (selectedProducts.length === 0) {
      alert('Please select at least one product to add.')
      return
    }

    // Get the current category ID (stored when modal was opened)
    const categoryId = modal.dataset.currentCategoryId
    if (!categoryId) {
      console.error('No category ID found for adding products')
      return
    }

    // Find the category and add products
    const category = this.findCategory(categoryId)
    if (category) {
      selectedProducts.forEach(product => {
        // Check if product already exists in category
        const existingProduct = category.products.find(p => p.productId === product.productId)
        if (!existingProduct) {
          category.products.push(product)
        }
      })

      // Re-render the category to show new products
      this.rerenderCategory(categoryId)
      this.updateSessionData()
    }

    // Close modal and clear selections
    const bootstrapModal = bootstrap.Modal.getInstance(modal)
    if (bootstrapModal) {
      bootstrapModal.hide()
    }

    // Clear all checkboxes
    checkedBoxes.forEach(checkbox => checkbox.checked = false)
  }

  // Update button state based on categories count
  updateButtonState() {
    const categoriesCount = this.categoriesValue.length
    const maxCategories = 12

    // Update button text and state
    if (this.hasAddCategoryButtonTarget) {
      const button = this.addCategoryButtonTarget

      if (categoriesCount >= maxCategories) {
        button.textContent = `Maximum ${maxCategories} Categories Reached`
        button.disabled = true
        button.classList.add('btn-secondary')
        button.classList.remove('btn-primary')
      } else {
        button.textContent = `+ Add Category`
        button.disabled = false
        button.classList.add('btn-primary')
        button.classList.remove('btn-secondary')
      }
    }
  }

  // Clear all rendered categories
  clearAllCategories() {
    const existingCategories = this.categoriesContainerTarget.querySelectorAll('[data-category-id]')
    existingCategories.forEach(category => category.remove())
  }

  // Render a single category
  renderCategory(category) {
    const categoryHtml = this.createCategoryHtml(category)
    // Add to categoriesList instead of categoriesContainer
    if (this.hasCategoriesListTarget) {
      this.categoriesListTarget.insertAdjacentHTML('beforeend', categoryHtml)
    } else {
      this.categoriesContainerTarget.insertAdjacentHTML('beforeend', categoryHtml)
    }
  }

  // Create HTML for a category (inline version)
  createCategoryHtml(category) {
    const productsHtml = category.products.map((product, index) =>
      this.createProductInCategoryHtml(product, index)
    ).join('')

    return `
      <div class="card mb-3" data-category-id="${category.categoryId}">
        <!-- Category Header - Inline Form -->
        <div class="card-header bg-light">
          <div class="row align-items-center">
            <div class="col-auto">
              <span class="text-muted" style="cursor: move;">≡</span>
            </div>
            <div class="col">
              <input type="text"
                     name="categoryName"
                     value="${category.categoryName}"
                     placeholder="Category Name (e.g., Protein Powders)"
                     class="form-control form-control-sm border-0 bg-transparent"
                     data-action="input->category-builder#updateCategoryName">
            </div>
            <div class="col-auto">
              <div class="input-group input-group-sm">
                <span class="input-group-text">Qty:</span>
                <input type="number"
                       name="selectionQuantity"
                       value="${category.selectionQuantity}"
                       min="1"
                       max="10"
                       class="form-control"
                       style="max-width: 70px;"
                       data-action="input->category-builder#updateSelectionQuantity">
              </div>
            </div>
            <div class="col-auto">
              <button type="button"
                      class="btn btn-primary btn-sm me-2"
                      data-action="click->category-builder#openProductModal"
                      data-category-id="${category.categoryId}"
                      title="Add products to category">
                + Add Products
              </button>
              <button type="button"
                      class="btn btn-outline-danger btn-sm"
                      data-action="click->category-builder#removeCategory"
                      title="Remove category">
                ×
              </button>
            </div>
          </div>
        </div>

        <!-- Products List -->
        <div class="card-body">
          <div class="products-container">

            <!-- Products List -->
            <div data-category-products>
              ${productsHtml}
            </div>

            <!-- Empty State -->
            <div class="text-center text-muted p-4 ${category.products.length > 0 ? 'd-none' : ''}" data-empty-state>
              <div class="mb-2" style="font-size: 2rem;">📦</div>
              <p class="mb-1">No products added yet</p>
              <small>Click "Add Products" to select products for this category</small>
            </div>

            <!-- Products Counter -->
            <div class="mt-2 small text-muted ${category.products.length === 0 ? 'd-none' : ''}" data-products-counter>
              <strong>${category.products.length}</strong> products added
              ${category.products.length > category.selectionQuantity ?
                `<span class="text-warning">(${category.products.length - category.selectionQuantity} more than needed)</span>` :
                category.products.length === category.selectionQuantity ?
                  '<span class="text-success">✓</span>' :
                  `<span class="text-primary">(need ${category.selectionQuantity - category.products.length} more)</span>`
              }
            </div>
          </div>
        </div>
      </div>
    `
  }

  // Create HTML for a product within a category
  createProductInCategoryHtml(product, displayOrder) {
    const isDefault = displayOrder === 0
    return `
      <div class="product-in-category flex items-center justify-between p-2 bg-white border border-gray-200 rounded-md ${isDefault ? 'ring-2 ring-blue-500' : ''}"
           data-product-id="${product.productId}"
           data-display-order="${displayOrder}">
        <div class="flex items-center space-x-3">
          <div class="drag-handle cursor-move text-gray-400">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"></path>
            </svg>
          </div>
          <div class="flex-1">
            <h5 class="text-sm font-medium text-gray-900">${product.variantTitle}</h5>
            <p class="text-xs text-gray-500">SKU: ${product.variantSku}</p>
            ${isDefault ? '<p class="text-xs text-blue-600 font-medium">Default selection</p>' : ''}
          </div>
        </div>
        <button type="button" 
                class="text-red-600 hover:text-red-800 p-1"
                data-action="click->category-builder#removeProduct"
                title="Remove product">
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    `
  }

  // Update category name
  updateCategoryName(event) {
    const categoryCard = event.target.closest('[data-category-id]')
    if (!categoryCard) {
      console.error('Could not find category card')
      return
    }

    const categoryId = categoryCard.dataset.categoryId
    const newName = event.target.value

    const category = this.findCategory(categoryId)
    if (category) {
      category.categoryName = newName
      this.updateSessionData()
    }
  }

  // Update selection quantity
  updateSelectionQuantity(event) {
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId
    const newQuantity = parseInt(event.target.value) || 1

    const category = this.findCategory(categoryId)
    if (category) {
      category.selectionQuantity = newQuantity
      this.updateProductsCounter(categoryCard, category)
      this.updateEmptyStateText(categoryCard, newQuantity)
      this.updateSessionData()
    }
  }

  // Open product selection modal for a category
  openProductModal(event) {
    const categoryId = event.target.dataset.categoryId
    console.log(`📂 Opening product modal for category: ${categoryId}`)

    // Store current category ID in the modal element
    const modalElement = document.getElementById('productSelectionModal')
    modalElement.dataset.currentCategoryId = categoryId

    // Show the modal (assuming Bootstrap 5)
    const modal = new bootstrap.Modal(modalElement)
    modal.show()
  }

  // Remove category
  removeCategory(event) {
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId

    if (confirm('Are you sure you want to remove this category?')) {
      // Remove from data
      this.categoriesValue = this.categoriesValue.filter(cat => cat.categoryId !== categoryId)

      // Remove from DOM
      categoryCard.remove()

      // Update button state
      this.updateButtonState()

      this.updateSessionData()
    }
  }

  // Remove product from category
  removeProduct(event) {
    const productElement = event.target.closest('.product-in-category')
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId
    const productId = productElement.dataset.productId

    const category = this.findCategory(categoryId)
    if (category) {
      // Remove product from data
      category.products = category.products.filter(p => p.productId !== productId)
      
      // Remove from DOM
      productElement.remove()
      
      // Update UI
      this.updateCategoryUI(categoryCard, category)
      this.updateSessionData()
    }
  }

  // Drag & Drop handlers
  dragOver(event) {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  dragEnter(event) {
    event.preventDefault()
    const dropZone = event.target.closest('.products-drop-zone')
    if (dropZone) {
      dropZone.classList.add('border-blue-500', 'bg-blue-50')
    }
  }

  dragLeave(event) {
    const dropZone = event.target.closest('.products-drop-zone')
    if (dropZone && !dropZone.contains(event.relatedTarget)) {
      dropZone.classList.remove('border-blue-500', 'bg-blue-50')
    }
  }

  drop(event) {
    event.preventDefault()
    const dropZone = event.target.closest('.products-drop-zone')
    const categoryCard = event.target.closest('.category-card')
    const categoryId = categoryCard.dataset.categoryId

    // Remove drag styling
    dropZone.classList.remove('border-blue-500', 'bg-blue-50')

    try {
      const productData = JSON.parse(event.dataTransfer.getData('application/json'))
      this.addProductToCategory(categoryId, productData)
    } catch (error) {
      console.error('Error parsing dropped product data:', error)
    }
  }

  // Add product to category
  addProductToCategory(categoryId, productData) {
    const category = this.findCategory(categoryId)
    if (!category) return

    // Check if product already exists in category
    const existingProduct = category.products.find(p => p.productId === productData.id)
    if (existingProduct) {
      alert('This product is already in this category')
      return
    }

    // Create product object
    const newProduct = {
      productId: productData.id,
      variantTitle: productData.name,
      variantSku: productData.sku,
      displayOrder: category.products.length,
      isDefault: category.products.length === 0 // First product is default
    }

    // Add to data
    category.products.push(newProduct)

    // Update UI
    const categoryCard = this.element.querySelector(`[data-category-id="${categoryId}"]`)
    this.updateCategoryUI(categoryCard, category)
    this.updateSessionData()
  }

  // Update category UI after changes
  updateCategoryUI(categoryCard, category) {
    const productsList = categoryCard.querySelector('[data-category-products]')
    const emptyState = categoryCard.querySelector('[data-empty-state]')
    
    // Update products list
    const productsHtml = category.products.map((product, index) => 
      this.createProductInCategoryHtml(product, index)
    ).join('')
    productsList.innerHTML = productsHtml

    // Update empty state visibility
    if (category.products.length > 0) {
      emptyState.classList.add('hidden')
    } else {
      emptyState.classList.remove('hidden')
    }

    // Update products counter
    this.updateProductsCounter(categoryCard, category)
  }

  // Update products counter
  updateProductsCounter(categoryCard, category) {
    const counter = categoryCard.querySelector('[data-products-counter]')
    const count = category.products.length
    const needed = category.selectionQuantity

    if (count === 0) {
      counter.classList.add('hidden')
    } else {
      counter.classList.remove('hidden')
      
      let statusText = ''
      if (count > needed) {
        statusText = `<span class="text-orange-600">(${count - needed} more than needed)</span>`
      } else if (count === needed) {
        statusText = '<span class="text-green-600">✓</span>'
      } else {
        statusText = `<span class="text-blue-600">(need ${needed - count} more)</span>`
      }

      counter.innerHTML = `<span class="font-medium">${count}</span> products added ${statusText}`
    }
  }

  // Update empty state text
  updateEmptyStateText(categoryCard, quantity) {
    const emptyState = categoryCard.querySelector('[data-empty-state] p:last-child')
    if (emptyState) {
      emptyState.textContent = `Customer can select ${quantity} product(s) from this category`
    }
  }

  // Continue to preview step
  continueToPreview() {
    if (this.categoriesValue.length === 0) {
      alert('Please create at least one category before continuing.')
      return
    }

    // Check if all categories have products
    const categoriesWithoutProducts = this.categoriesValue.filter(cat => cat.products.length === 0)
    if (categoriesWithoutProducts.length > 0) {
      const categoryNames = categoriesWithoutProducts.map(cat => cat.categoryName || 'Unnamed Category').join(', ')
      if (!confirm(`The following categories have no products: ${categoryNames}. Continue anyway?`)) {
        return
      }
    }

    // Update session data and submit form
    this.updateSessionData()

    // Submit the form to continue to preview
    const form = this.element.closest('form')
    if (form) {
      // Update hidden input with categories data
      const categoriesInput = document.getElementById('categories_data_input')
      if (categoriesInput) {
        categoriesInput.value = JSON.stringify(this.categoriesValue)
      }

      form.submit()
    }
  }

  // Update session data via AJAX
  updateSession() {
    // This will be called when categories are updated
    // For now, we'll just update the hidden input
    const categoriesInput = document.getElementById('categories_data_input')
    if (categoriesInput) {
      categoriesInput.value = JSON.stringify(this.categoriesValue)
    }

    // Update continue button state
    this.updateContinueButton()
  }

  // Update continue button state
  updateContinueButton() {
    const continueBtn = document.getElementById('continueToPreview')

    if (continueBtn) {
      continueBtn.disabled = this.categoriesValue.length === 0
    }
  }

  // Re-render a specific category
  rerenderCategory(categoryId) {
    const existingCategoryCard = this.element.querySelector(`[data-category-id="${categoryId}"]`)
    if (existingCategoryCard) {
      const category = this.findCategory(categoryId)
      if (category) {
        const newCategoryHtml = this.createCategoryHtml(category)
        existingCategoryCard.outerHTML = newCategoryHtml
      }
    }
  }

  // Helper methods
  findCategory(categoryId) {
    return this.categoriesValue.find(cat => cat.categoryId === categoryId)
  }

  updateSessionData() {
    // Update the Stimulus value
    this.categoriesValue = [...this.categoriesValue]

    // Update continue button
    this.updateContinueButton()

    // Dispatch event to update session
    this.dispatch('categoriesUpdated', {
      detail: { categories: this.categoriesValue }
    })
  }

  // Global drag & drop setup (for cleanup)
  setupGlobalDragListeners() {
    // Add any global listeners if needed
  }

  cleanupGlobalDragListeners() {
    // Clean up any global listeners
  }
}
