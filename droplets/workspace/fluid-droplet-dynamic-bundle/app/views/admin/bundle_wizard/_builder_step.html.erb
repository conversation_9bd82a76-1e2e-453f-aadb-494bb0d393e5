<div class="step-content" id="step-2">
  <form data-controller="category-builder"
        data-category-builder-categories-value="<%= (@wizard&.categories || []).to_json %>"
        data-action="submit->category-builder#saveCategories">

    <div class="row">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
          <div>
            <h4 class="mb-1">📂 Bundle Categories</h4>
            <p class="text-muted mb-0">Configure product categories for your bundle</p>
          </div>
          <div>
            <button type="button"
                    class="btn btn-outline-primary"
                    data-action="click->category-builder#fetchCategories"
                    data-category-builder-target="fetchCategoriesButton">
              🔄 Fetch Categories
            </button>
          </div>
        </div>

        <div class="row">
          <div class="col-12">
            <!-- Categories Container -->
            <div data-category-builder-target="categoriesContainer">
              <!-- Categories will be added here -->
              <div data-category-builder-target="categoriesList">
                <!-- Dynamic categories -->
              </div>
            </div>

            <!-- Add Category Button (always visible at bottom) -->
            <div class="mt-4 text-center">
              <button type="button"
                      class="btn btn-primary"
                      data-action="click->category-builder#addCategory"
                      data-category-builder-target="addCategoryButton">
                + Add Category
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Navigation Buttons -->
    <div class="d-flex justify-content-between mt-5">
      <button type="button" class="btn btn-outline-secondary" onclick="previousStep()">
        ← Back to Bundle Info
      </button>
      <div>
        <button type="button" class="btn btn-outline-primary me-2" onclick="saveDraft()">
          Save Draft
        </button>
        <button type="button" class="btn btn-primary" onclick="nextStep()" id="continueToPreview">
          Continue to Preview →
        </button>
      </div>
    </div>

  </form>

</div>

<!-- Product Selection Modal -->
<div class="modal fade" id="productSelectionModal" tabindex="-1" aria-labelledby="productSelectionModalLabel" aria-hidden="true"
     data-controller="product-search"
     data-product-search-search-url-value="/admin/products/search">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="productSelectionModalLabel">Add Products to Category</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>

      <div class="modal-body" style="max-height: 60vh; overflow-y: auto;">
        <!-- Search Input -->
        <div class="mb-3">
          <label for="productSearch" class="form-label">Search Products</label>
          <input type="text" 
                 class="form-control" 
                 data-product-search-target="searchInput"
                 data-action="input->product-search#searchProducts"
                 placeholder="Search by name or SKU...">
        </div>

        <!-- Loading State -->
        <div class="text-center py-4 hidden" data-product-search-target="productsLoading">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <div class="mt-2">Loading products...</div>
        </div>

        <!-- Products Container -->
        <div data-product-search-target="productsContainer">
          <!-- Products List (populated dynamically) -->
          <div data-product-search-target="productsList">
            <!-- Products will be loaded here dynamically -->
          </div>
          
          <!-- Empty State -->
          <div class="text-center py-4 hidden" data-product-search-target="productsEmpty">
            <div class="text-muted">
              <div style="font-size: 3rem;">📦</div>
              <p class="mt-2">No products found</p>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" 
                class="btn btn-primary" 
                data-action="click->category-builder#addSelectedProducts">
          Add Selected Products
        </button>
      </div>
    </div>
  </div>
</div>

<style>
/* Category Builder Interface Layout */
.category-builder-interface {
  height: calc(100vh - 210px);
  min-height: 500px;
}

/* Category Cards */
.category-card {
  background: white;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  margin-bottom: 1rem;
  transition: all 0.2s ease;
}

.category-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* Products Drop Zone */
.products-drop-zone {
  min-height: 100px;
  transition: all 0.3s ease;
}

.products-drop-zone.border-blue-500 {
  border-color: #3b82f6 !important;
  background-color: #eff6ff !important;
}

/* Product Cards */
.product-card {
  transition: all 0.2s ease;
  cursor: move;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.product-card.opacity-50 {
  opacity: 0.5;
}

/* Product in Category */
.product-in-category {
  transition: all 0.2s ease;
}

.product-in-category:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Hidden utility */
.hidden {
  display: none !important;
}
</style>
