Rails.application.routes.draw do
  root "home#index"

  devise_for :users

  post "webhook", to: "webhooks#create", as: :webhook
  post "webhooks", to: "webhooks#create", as: :webhooks

  namespace :admin do
    get "dashboard/index"
    resource :droplet, only: %i[ create update ]
    resources :settings, only: %i[ index edit update ]
    resources :users
    resources :callbacks, only: %i[ index show edit update ] do
      post :sync, on: :collection
    end

    # Products API for wizard
    resources :products, only: [:index] do
      collection do
        get :search
      end
    end

    # Categories API for wizard
    resources :categories, only: [:index] do
      collection do
        get :search
        get 'products/:category_id', to: 'categories#products_by_category', as: :products_by_category
      end
    end

    # Dynamic Bundle management routes
    resources :bundles do
      member do
        patch :toggle_status
      end

      # Bundle Creation Wizard routes
      collection do
        get 'wizard', to: 'bundle_wizard#start', as: :start_bundle_wizard
        get 'wizard/step/:step', to: 'bundle_wizard#step', as: :bundle_wizard_step
        post 'wizard/step/:step', to: 'bundle_wizard#process_step', as: :process_bundle_wizard_step
        post 'wizard/complete', to: 'bundle_wizard#complete', as: :complete_bundle_wizard
        get 'wizard/products/:category_id', to: 'bundle_wizard#load_products', as: :wizard_load_products
        get 'search_products', to: 'bundles#search_products', as: :search_products
      end



      # Bundle Builder routes (for editing existing bundles)
      collection do
        post 'builder', to: 'bundle_builder#create', as: :bundle_builder
        get 'builder/new', to: 'bundle_builder#new_builder', as: :new_bundle_builder
      end

      member do
        get 'edit/builder', to: 'bundle_builder#edit', as: :edit_bundle_builder
        patch 'builder', to: 'bundle_builder#update', as: :update_bundle_builder
      end

      # Nested categories routes
      resources :categories do
        member do
          patch :move_up
          patch :move_down
        end

        # Nested products routes for assignment
        resources :products, only: [:index] do
          member do
            post :assign
            delete :unassign
            patch :set_default
            patch :move_up
            patch :move_down
          end
        end
      end

      # Export routes
      resource :export, only: [:show, :create], controller: 'exports' do
        member do
          get :preview
          post :validate
          get :download
        end
      end
    end
  end

  get "up" => "rails/health#show", as: :rails_health_check

  # Test endpoint without authentication
  get "test_api", to: "home#test_api"
end
